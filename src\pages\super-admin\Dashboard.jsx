import React, { useState, useEffect } from 'react';
import { BarChart3, Users, Shield, Database, TrendingUp, Download, AlertTriangle } from 'lucide-react';
import { But<PERSON> } from '../../components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card';
import { Badge } from '../../components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/ui/table';
import { Progress } from '../../components/ui/progress';
import { useAuth } from '../../context/AuthContext';
import { adminAPI } from '../../services/api';

const SuperAdminDashboard = () => {
  const [systemStats, setSystemStats] = useState({
    totalUsers: 1250,
    totalAdmins: 8,
    totalVehicles: 450,
    totalBookings: 2840,
    totalRevenue: 125000,
    systemHealth: 98.5,
    activeUsers: 89,
    serverUptime: '99.9%'
  });
  const [reports, setReports] = useState([]);
  const [loading, setLoading] = useState(true);

  const { user } = useAuth();

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      // Mock system reports
      const mockReports = [
        {
          id: '1',
          title: 'Monthly Revenue Report',
          type: 'revenue',
          period: 'January 2024',
          status: 'completed',
          generatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
          size: '2.4 MB'
        },
        {
          id: '2',
          title: 'User Activity Analysis',
          type: 'users',
          period: 'Q4 2023',
          status: 'completed',
          generatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
          size: '1.8 MB'
        },
        {
          id: '3',
          title: 'System Performance Report',
          type: 'system',
          period: 'December 2023',
          status: 'processing',
          generatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
          size: 'Generating...'
        }
      ];

      setReports(mockReports);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const generateReport = (type) => {
    const newReport = {
      id: Date.now().toString(),
      title: `${type.charAt(0).toUpperCase() + type.slice(1)} Report`,
      type: type,
      period: new Date().toLocaleDateString('en-US', { month: 'long', year: 'numeric' }),
      status: 'processing',
      generatedAt: new Date(),
      size: 'Generating...'
    };

    setReports(prev => [newReport, ...prev]);

    // Simulate report generation
    setTimeout(() => {
      setReports(prev => prev.map(report => 
        report.id === newReport.id 
          ? { ...report, status: 'completed', size: `${(Math.random() * 3 + 1).toFixed(1)} MB` }
          : report
      ));
    }, 3000);
  };

  const downloadReport = (report) => {
    // Mock download
    const reportData = `
SYSTEM REPORT - ${report.title}
================================
Generated: ${report.generatedAt.toLocaleString()}
Period: ${report.period}
Type: ${report.type}

SUMMARY
-------
Total Users: ${systemStats.totalUsers}
Total Vehicles: ${systemStats.totalVehicles}
Total Bookings: ${systemStats.totalBookings}
Total Revenue: ₹${systemStats.totalRevenue.toLocaleString()}
System Health: ${systemStats.systemHealth}%
Server Uptime: ${systemStats.serverUptime}

DETAILED METRICS
----------------
Active Users: ${systemStats.activeUsers}
Admin Users: ${systemStats.totalAdmins}
Platform Performance: Excellent
Security Status: All systems secure

================================
Report generated by Vehicle Rental Platform
Super Admin Dashboard
    `;
    
    const blob = new Blob([reportData], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${report.title.toLowerCase().replace(/\s+/g, '-')}-${report.period.replace(/\s+/g, '-')}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getReportStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'processing':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getReportIcon = (type) => {
    switch (type) {
      case 'revenue':
        return '💰';
      case 'users':
        return '👥';
      case 'system':
        return '⚙️';
      case 'security':
        return '🔒';
      default:
        return '📊';
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map(i => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-muted rounded w-3/4"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-muted rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Super Admin Dashboard</h1>
        <p className="text-muted-foreground">System overview and administrative controls</p>
      </div>

      {/* System Health Alert */}
      {systemStats.systemHealth < 95 && (
        <Card className="border-orange-200 bg-orange-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-600" />
              <div>
                <h3 className="font-semibold text-orange-800">System Health Warning</h3>
                <p className="text-sm text-orange-700">
                  System health is below optimal levels ({systemStats.systemHealth}%). Please review system performance.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* System Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{systemStats.totalUsers.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              +{systemStats.activeUsers} active now
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Health</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{systemStats.systemHealth}%</div>
            <Progress value={systemStats.systemHealth} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{systemStats.totalRevenue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Platform lifetime earnings
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Server Uptime</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{systemStats.serverUptime}</div>
            <p className="text-xs text-muted-foreground">
              Last 30 days
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Stats */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Platform Statistics</CardTitle>
            <CardDescription>Key metrics across the platform</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm">Total Vehicles</span>
              <span className="font-semibold">{systemStats.totalVehicles}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Total Bookings</span>
              <span className="font-semibold">{systemStats.totalBookings.toLocaleString()}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Admin Users</span>
              <span className="font-semibold">{systemStats.totalAdmins}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Active Sessions</span>
              <span className="font-semibold">{systemStats.activeUsers}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Generate system reports and analytics</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <Button 
              variant="outline" 
              className="w-full justify-start"
              onClick={() => generateReport('revenue')}
            >
              <TrendingUp className="h-4 w-4 mr-2" />
              Generate Revenue Report
            </Button>
            <Button 
              variant="outline" 
              className="w-full justify-start"
              onClick={() => generateReport('users')}
            >
              <Users className="h-4 w-4 mr-2" />
              Generate User Analytics
            </Button>
            <Button 
              variant="outline" 
              className="w-full justify-start"
              onClick={() => generateReport('system')}
            >
              <Database className="h-4 w-4 mr-2" />
              Generate System Report
            </Button>
            <Button 
              variant="outline" 
              className="w-full justify-start"
              onClick={() => generateReport('security')}
            >
              <Shield className="h-4 w-4 mr-2" />
              Generate Security Audit
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Reports */}
      <Card>
        <CardHeader>
          <CardTitle>System Reports</CardTitle>
          <CardDescription>Generated reports and analytics</CardDescription>
        </CardHeader>
        <CardContent>
          {reports.length === 0 ? (
            <div className="text-center py-8">
              <BarChart3 className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-semibold mb-2">No reports generated</h3>
              <p className="text-muted-foreground">
                Generate your first system report using the quick actions above
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Report</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Period</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Generated</TableHead>
                    <TableHead>Size</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {reports.map((report) => (
                    <TableRow key={report.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <span>{getReportIcon(report.type)}</span>
                          <span className="font-medium">{report.title}</span>
                        </div>
                      </TableCell>
                      <TableCell className="capitalize">{report.type}</TableCell>
                      <TableCell>{report.period}</TableCell>
                      <TableCell>
                        <Badge className={getReportStatusColor(report.status)}>
                          {report.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {report.generatedAt.toLocaleDateString()}
                      </TableCell>
                      <TableCell>{report.size}</TableCell>
                      <TableCell>
                        {report.status === 'completed' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => downloadReport(report)}
                          >
                            <Download className="h-4 w-4 mr-2" />
                            Download
                          </Button>
                        )}
                        {report.status === 'processing' && (
                          <Badge variant="outline">Processing...</Badge>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default SuperAdminDashboard;
