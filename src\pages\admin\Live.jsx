import React, { useState, useEffect } from 'react';
import { Activity, RefreshCw, User, Car, CreditCard, AlertCircle, CheckCircle, Clock } from 'lucide-react';
import { Button } from '../../components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card';
import { Badge } from '../../components/ui/badge';
import { Separator } from '../../components/ui/separator';
import { useToast } from '../../hooks/use-toast';

const AdminLive = () => {
  const [activities, setActivities] = useState([]);
  const [loading, setLoading] = useState(false);
  const [lastUpdate, setLastUpdate] = useState(new Date());

  const { toast } = useToast();

  // Mock live activities
  const mockActivities = [
    {
      id: '1',
      type: 'booking_created',
      title: 'New Booking Request',
      description: '<PERSON> requested to book Honda Activa 6G',
      user: '<PERSON>',
      vehicle: 'Honda Activa 6G',
      amount: 400,
      timestamp: new Date(Date.now() - 2 * 60 * 1000), // 2 minutes ago
      status: 'pending'
    },
    {
      id: '2',
      type: 'payment_completed',
      title: 'Payment Received',
      description: 'Payment of ₹1200 received for Maruti Swift booking',
      user: 'Jane Smith',
      vehicle: 'Maruti Swift',
      amount: 1200,
      timestamp: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
      status: 'completed'
    },
    {
      id: '3',
      type: 'user_registered',
      title: 'New User Registration',
      description: 'Mike Johnson joined as a renter',
      user: 'Mike Johnson',
      timestamp: new Date(Date.now() - 8 * 60 * 1000), // 8 minutes ago
      status: 'info'
    },
    {
      id: '4',
      type: 'vehicle_listed',
      title: 'New Vehicle Listed',
      description: 'Sarah Wilson listed Royal Enfield Classic 350',
      user: 'Sarah Wilson',
      vehicle: 'Royal Enfield Classic 350',
      timestamp: new Date(Date.now() - 12 * 60 * 1000), // 12 minutes ago
      status: 'info'
    },
    {
      id: '5',
      type: 'booking_cancelled',
      title: 'Booking Cancelled',
      description: 'Tom Brown cancelled his bicycle booking',
      user: 'Tom Brown',
      vehicle: 'Hero Cycle',
      timestamp: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago
      status: 'cancelled'
    },
    {
      id: '6',
      type: 'payment_failed',
      title: 'Payment Failed',
      description: 'Payment failed for booking #BK123',
      user: 'Alice Cooper',
      amount: 800,
      timestamp: new Date(Date.now() - 18 * 60 * 1000), // 18 minutes ago
      status: 'error'
    }
  ];

  useEffect(() => {
    loadActivities();
    
    // Set up auto-refresh every 30 seconds
    const interval = setInterval(() => {
      loadActivities();
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const loadActivities = async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Add some randomness to simulate real-time updates
      const shuffledActivities = [...mockActivities].sort(() => Math.random() - 0.5);
      setActivities(shuffledActivities);
      setLastUpdate(new Date());
    } catch (error) {
      toast({
        title: "Failed to load activities",
        description: "Unable to fetch live activities. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const getActivityIcon = (type) => {
    switch (type) {
      case 'booking_created':
        return <Car className="h-4 w-4" />;
      case 'payment_completed':
      case 'payment_failed':
        return <CreditCard className="h-4 w-4" />;
      case 'user_registered':
        return <User className="h-4 w-4" />;
      case 'vehicle_listed':
        return <Car className="h-4 w-4" />;
      case 'booking_cancelled':
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <Activity className="h-4 w-4" />;
    }
  };

  const getActivityColor = (status) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'pending':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'error':
      case 'cancelled':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'info':
      default:
        return 'text-blue-600 bg-blue-50 border-blue-200';
    }
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-100 text-green-800">Completed</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case 'error':
        return <Badge className="bg-red-100 text-red-800">Error</Badge>;
      case 'cancelled':
        return <Badge className="bg-red-100 text-red-800">Cancelled</Badge>;
      case 'info':
      default:
        return <Badge className="bg-blue-100 text-blue-800">Info</Badge>;
    }
  };

  const formatTimeAgo = (timestamp) => {
    const now = new Date();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;
    
    const days = Math.floor(hours / 24);
    return `${days}d ago`;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Live Activity</h1>
          <p className="text-muted-foreground">Real-time platform activity monitoring</p>
        </div>
        <div className="flex items-center gap-4">
          <div className="text-sm text-muted-foreground">
            Last updated: {lastUpdate.toLocaleTimeString()}
          </div>
          <Button onClick={loadActivities} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Activity Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Sessions</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">24</div>
            <p className="text-xs text-muted-foreground">Users online now</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Actions</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {activities.filter(a => a.status === 'pending').length}
            </div>
            <p className="text-xs text-muted-foreground">Require attention</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Recent Errors</CardTitle>
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {activities.filter(a => a.status === 'error').length}
            </div>
            <p className="text-xs text-muted-foreground">In last hour</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed Today</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {activities.filter(a => a.status === 'completed').length}
            </div>
            <p className="text-xs text-muted-foreground">Successful actions</p>
          </CardContent>
        </Card>
      </div>

      {/* Live Activity Feed */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Live Activity Feed
          </CardTitle>
          <CardDescription>
            Real-time updates from across the platform
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading && activities.length === 0 ? (
            <div className="space-y-4">
              {[1, 2, 3, 4, 5].map(i => (
                <div key={i} className="animate-pulse">
                  <div className="flex items-start gap-4 p-4 border rounded-lg">
                    <div className="w-8 h-8 bg-muted rounded-full"></div>
                    <div className="flex-1 space-y-2">
                      <div className="h-4 bg-muted rounded w-3/4"></div>
                      <div className="h-3 bg-muted rounded w-1/2"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {activities.map((activity, index) => (
                <div key={activity.id}>
                  <div className={`flex items-start gap-4 p-4 border rounded-lg ${getActivityColor(activity.status)}`}>
                    <div className="flex-shrink-0 w-8 h-8 rounded-full bg-white border flex items-center justify-center">
                      {getActivityIcon(activity.type)}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <h4 className="font-medium">{activity.title}</h4>
                        <div className="flex items-center gap-2">
                          {getStatusBadge(activity.status)}
                          <span className="text-xs text-muted-foreground">
                            {formatTimeAgo(activity.timestamp)}
                          </span>
                        </div>
                      </div>
                      
                      <p className="text-sm text-muted-foreground mb-2">
                        {activity.description}
                      </p>
                      
                      <div className="flex items-center gap-4 text-xs">
                        {activity.user && (
                          <div className="flex items-center gap-1">
                            <User className="h-3 w-3" />
                            <span>{activity.user}</span>
                          </div>
                        )}
                        {activity.vehicle && (
                          <div className="flex items-center gap-1">
                            <Car className="h-3 w-3" />
                            <span>{activity.vehicle}</span>
                          </div>
                        )}
                        {activity.amount && (
                          <div className="flex items-center gap-1">
                            <CreditCard className="h-3 w-3" />
                            <span>₹{activity.amount}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  {index < activities.length - 1 && <Separator className="my-4" />}
                </div>
              ))}
              
              {activities.length === 0 && (
                <div className="text-center py-8">
                  <Activity className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-semibold mb-2">No recent activity</h3>
                  <p className="text-muted-foreground">
                    Activity will appear here as users interact with the platform
                  </p>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminLive;
