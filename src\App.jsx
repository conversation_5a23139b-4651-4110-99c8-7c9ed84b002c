import { useState } from 'react'
import reactLogo from './assets/react.svg'
import viteLogo from '/vite.svg'
import './App.css'
import { Button } from './components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from './components/ui/card'
import { Input } from './components/ui/input'

function App() {
  const [count, setCount] = useState(0)

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center gap-4 mb-4">
            <a href="https://vite.dev" target="_blank">
              <img src={viteLogo} className="h-12 w-12" alt="Vite logo" />
            </a>
            <a href="https://react.dev" target="_blank">
              <img src={reactLogo} className="h-12 w-12 animate-spin" alt="React logo" />
            </a>
          </div>
          <CardTitle>Vite + React + shadcn/ui</CardTitle>
          <CardDescription>
            A modern React app with Tailwind CSS and shadcn/ui components
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-center">
            <Button onClick={() => setCount((count) => count + 1)} className="w-full">
              Count is {count}
            </Button>
          </div>
          <Input
            placeholder="Try typing something..."
            className="w-full"
          />
        </CardContent>
        <CardFooter className="text-center">
          <p className="text-sm text-muted-foreground">
            Edit <code>src/App.jsx</code> and save to test HMR
          </p>
        </CardFooter>
      </Card>
    </div>
  )
}

export default App
