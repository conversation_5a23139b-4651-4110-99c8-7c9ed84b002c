import React, { useState } from 'react';
import { Calendar, Clock, CreditCard, MapPin } from 'lucide-react';
import { Button } from '../ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '../ui/dialog';
import { Label } from '../ui/label';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Separator } from '../ui/separator';
import { useAuth } from '../../context/AuthContext';
import { useToast } from '../../hooks/use-toast';
import { bookingAPI } from '../../services/api';

const BookingModal = ({ vehicle, open, onClose }) => {
  const [bookingData, setBookingData] = useState({
    startDate: '',
    endDate: '',
    startTime: '',
    endTime: '',
    rentalType: 'hourly', // hourly or daily
    notes: '',
    pickupLocation: vehicle?.location || ''
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const { user } = useAuth();
  const { toast } = useToast();

  const calculateTotal = () => {
    if (!bookingData.startDate || !bookingData.endDate) return 0;

    const start = new Date(`${bookingData.startDate}T${bookingData.startTime || '00:00'}`);
    const end = new Date(`${bookingData.endDate}T${bookingData.endTime || '23:59'}`);
    
    if (bookingData.rentalType === 'daily') {
      const days = Math.ceil((end - start) / (1000 * 60 * 60 * 24));
      return days * vehicle.pricePerDay;
    } else {
      const hours = Math.ceil((end - start) / (1000 * 60 * 60));
      return hours * vehicle.pricePerHour;
    }
  };

  const validateForm = () => {
    const newErrors = {};
    const now = new Date();
    const start = new Date(`${bookingData.startDate}T${bookingData.startTime || '00:00'}`);
    const end = new Date(`${bookingData.endDate}T${bookingData.endTime || '23:59'}`);

    if (!bookingData.startDate) {
      newErrors.startDate = 'Start date is required';
    } else if (start < now) {
      newErrors.startDate = 'Start date cannot be in the past';
    }

    if (!bookingData.endDate) {
      newErrors.endDate = 'End date is required';
    } else if (end <= start) {
      newErrors.endDate = 'End date must be after start date';
    }

    if (bookingData.rentalType === 'hourly') {
      if (!bookingData.startTime) {
        newErrors.startTime = 'Start time is required for hourly rental';
      }
      if (!bookingData.endTime) {
        newErrors.endTime = 'End time is required for hourly rental';
      }
    }

    if (!bookingData.pickupLocation.trim()) {
      newErrors.pickupLocation = 'Pickup location is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!user?.aadhaarVerified) {
      toast({
        title: "Verification Required",
        description: "Please verify your Aadhaar before booking a vehicle.",
        variant: "destructive",
      });
      return;
    }

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      const total = calculateTotal();
      const booking = {
        vehicleId: vehicle.id,
        vehicleTitle: vehicle.title,
        userId: user.id,
        userName: user.name,
        startDate: bookingData.startDate,
        endDate: bookingData.endDate,
        startTime: bookingData.startTime,
        endTime: bookingData.endTime,
        rentalType: bookingData.rentalType,
        totalAmount: total,
        status: 'pending',
        paymentStatus: 'pending',
        notes: bookingData.notes,
        pickupLocation: bookingData.pickupLocation,
        createdAt: new Date().toISOString()
      };

      await bookingAPI.create(booking);
      
      toast({
        title: "Booking Request Sent",
        description: "Your booking request has been sent to the vehicle owner. You'll receive a confirmation soon.",
      });
      
      onClose();
    } catch (error) {
      toast({
        title: "Booking Failed",
        description: "Something went wrong. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (field, value) => {
    setBookingData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const total = calculateTotal();

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Book {vehicle?.title}</DialogTitle>
          <DialogDescription>
            Fill in the details to book this vehicle
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Vehicle Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Vehicle Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between">
                <span>Vehicle:</span>
                <span className="font-medium">{vehicle?.title}</span>
              </div>
              <div className="flex justify-between">
                <span>Type:</span>
                <span className="capitalize">{vehicle?.type}</span>
              </div>
              <div className="flex justify-between">
                <span>Location:</span>
                <span>{vehicle?.location}</span>
              </div>
              <div className="flex justify-between">
                <span>Hourly Rate:</span>
                <span>₹{vehicle?.pricePerHour}</span>
              </div>
              <div className="flex justify-between">
                <span>Daily Rate:</span>
                <span>₹{vehicle?.pricePerDay}</span>
              </div>
            </CardContent>
          </Card>

          {/* Rental Type */}
          <div className="space-y-2">
            <Label>Rental Type</Label>
            <Select value={bookingData.rentalType} onValueChange={(value) => handleChange('rentalType', value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="hourly">Hourly Rental</SelectItem>
                <SelectItem value="daily">Daily Rental</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Date and Time Selection */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="startDate">Start Date</Label>
              <Input
                id="startDate"
                type="date"
                value={bookingData.startDate}
                onChange={(e) => handleChange('startDate', e.target.value)}
                min={new Date().toISOString().split('T')[0]}
              />
              {errors.startDate && <p className="text-sm text-destructive">{errors.startDate}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="endDate">End Date</Label>
              <Input
                id="endDate"
                type="date"
                value={bookingData.endDate}
                onChange={(e) => handleChange('endDate', e.target.value)}
                min={bookingData.startDate || new Date().toISOString().split('T')[0]}
              />
              {errors.endDate && <p className="text-sm text-destructive">{errors.endDate}</p>}
            </div>
          </div>

          {bookingData.rentalType === 'hourly' && (
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="startTime">Start Time</Label>
                <Input
                  id="startTime"
                  type="time"
                  value={bookingData.startTime}
                  onChange={(e) => handleChange('startTime', e.target.value)}
                />
                {errors.startTime && <p className="text-sm text-destructive">{errors.startTime}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="endTime">End Time</Label>
                <Input
                  id="endTime"
                  type="time"
                  value={bookingData.endTime}
                  onChange={(e) => handleChange('endTime', e.target.value)}
                />
                {errors.endTime && <p className="text-sm text-destructive">{errors.endTime}</p>}
              </div>
            </div>
          )}

          {/* Pickup Location */}
          <div className="space-y-2">
            <Label htmlFor="pickupLocation">Pickup Location</Label>
            <Input
              id="pickupLocation"
              placeholder="Enter pickup location"
              value={bookingData.pickupLocation}
              onChange={(e) => handleChange('pickupLocation', e.target.value)}
            />
            {errors.pickupLocation && <p className="text-sm text-destructive">{errors.pickupLocation}</p>}
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes">Additional Notes (Optional)</Label>
            <Textarea
              id="notes"
              placeholder="Any special requirements or notes for the owner..."
              value={bookingData.notes}
              onChange={(e) => handleChange('notes', e.target.value)}
              rows={3}
            />
          </div>

          {/* Booking Summary */}
          {total > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />
                  Booking Summary
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span>Rental Type:</span>
                  <span className="capitalize">{bookingData.rentalType}</span>
                </div>
                <div className="flex justify-between">
                  <span>Rate:</span>
                  <span>
                    ₹{bookingData.rentalType === 'daily' ? vehicle?.pricePerDay : vehicle?.pricePerHour}
                    /{bookingData.rentalType === 'daily' ? 'day' : 'hour'}
                  </span>
                </div>
                <Separator />
                <div className="flex justify-between font-semibold text-lg">
                  <span>Total Amount:</span>
                  <span>₹{total}</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  Payment will be processed after owner confirmation
                </p>
              </CardContent>
            </Card>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button type="button" variant="outline" className="flex-1" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" className="flex-1" disabled={loading || total === 0}>
              {loading ? 'Booking...' : `Book for ₹${total}`}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default BookingModal;
