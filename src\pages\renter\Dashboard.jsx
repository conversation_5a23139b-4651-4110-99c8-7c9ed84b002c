import React, { useState, useEffect } from 'react';
import { Plus, Car, DollarSign, Calendar, TrendingUp, Eye, Edit, Trash2 } from 'lucide-react';
import { Link } from 'react-router-dom';
import { Button } from '../../components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card';
import { Badge } from '../../components/ui/badge';
import { Alert, AlertDescription } from '../../components/ui/alert';
import { useAuth } from '../../context/AuthContext';
import { vehicleAPI, bookingAPI } from '../../services/api';

const RenterDashboard = () => {
  const { user } = useAuth();
  const [vehicles, setVehicles] = useState([]);
  const [bookings, setBookings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalVehicles: 0,
    activeBookings: 0,
    monthlyEarnings: 0,
    totalEarnings: 0
  });

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      const [vehiclesResponse, bookingsResponse] = await Promise.all([
        vehicleAPI.getAll(),
        bookingAPI.getByRenterId(user.id)
      ]);

      // Filter vehicles owned by current renter
      const myVehicles = vehiclesResponse.data.filter(v => v.renterId === user.id);
      const myBookings = bookingsResponse.data;

      setVehicles(myVehicles);
      setBookings(myBookings);

      // Calculate stats
      const activeBookings = myBookings.filter(b => b.status === 'active').length;
      const monthlyEarnings = myBookings
        .filter(b => {
          const bookingDate = new Date(b.createdAt);
          const currentDate = new Date();
          return bookingDate.getMonth() === currentDate.getMonth() &&
                 bookingDate.getFullYear() === currentDate.getFullYear() &&
                 b.paymentStatus === 'paid';
        })
        .reduce((sum, b) => sum + b.totalAmount, 0);

      const totalEarnings = myBookings
        .filter(b => b.paymentStatus === 'paid')
        .reduce((sum, b) => sum + b.totalAmount, 0);

      setStats({
        totalVehicles: myVehicles.length,
        activeBookings,
        monthlyEarnings,
        totalEarnings
      });
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getVehicleStatusColor = (available) => {
    return available ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map(i => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-muted rounded w-3/4"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-muted rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Welcome back, {user?.name}!</h1>
          <p className="text-muted-foreground">Manage your vehicle rentals and track earnings</p>
        </div>
        <Link to="/renter/upload">
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Add Vehicle
          </Button>
        </Link>
      </div>

      {/* Document Verification Alert */}
      {!user?.documentsVerified && (
        <Alert className="border-orange-200 bg-orange-50">
          <AlertTriangle className="h-4 w-4 text-orange-600" />
          <AlertDescription className="flex items-center justify-between">
            <div>
              <strong>Document Verification Required</strong>
              <p className="text-sm mt-1">
                Please upload your documents to start listing vehicles for rent.
              </p>
            </div>
            <Button variant="outline" size="sm">
              Upload Documents
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Vehicles</CardTitle>
            <Car className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalVehicles}</div>
            <p className="text-xs text-muted-foreground">
              {vehicles.filter(v => v.available).length} available
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Bookings</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeBookings}</div>
            <p className="text-xs text-muted-foreground">Currently rented</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">This Month</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{stats.monthlyEarnings}</div>
            <p className="text-xs text-muted-foreground">Monthly earnings</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Earnings</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{stats.totalEarnings}</div>
            <p className="text-xs text-muted-foreground">All time</p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <Link to="/renter/upload" className="block">
              <Button variant="outline" className="w-full justify-start">
                <Plus className="h-4 w-4 mr-2" />
                Add New Vehicle
              </Button>
            </Link>
            <Link to="/renter/vehicles" className="block">
              <Button variant="outline" className="w-full justify-start">
                <Car className="h-4 w-4 mr-2" />
                Manage Vehicles
              </Button>
            </Link>
            <Link to="/renter/history" className="block">
              <Button variant="outline" className="w-full justify-start">
                <DollarSign className="h-4 w-4 mr-2" />
                View Earnings
              </Button>
            </Link>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Recent Bookings</CardTitle>
            <CardDescription>Latest rental requests</CardDescription>
          </CardHeader>
          <CardContent>
            {bookings.slice(0, 3).length === 0 ? (
              <p className="text-sm text-muted-foreground">No recent bookings</p>
            ) : (
              <div className="space-y-3">
                {bookings.slice(0, 3).map((booking) => (
                  <div key={booking.id} className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium">{booking.vehicleTitle}</p>
                      <p className="text-xs text-muted-foreground">
                        {booking.userName} • ₹{booking.totalAmount}
                      </p>
                    </div>
                    <Badge className={
                      booking.status === 'active' ? 'bg-green-100 text-green-800' :
                      booking.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-gray-100 text-gray-800'
                    }>
                      {booking.status}
                    </Badge>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>My Vehicles</CardTitle>
            <CardDescription>Vehicle status overview</CardDescription>
          </CardHeader>
          <CardContent>
            {vehicles.slice(0, 3).length === 0 ? (
              <div className="text-center py-4">
                <Car className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                <p className="text-sm text-muted-foreground mb-2">No vehicles listed</p>
                <Link to="/renter/upload">
                  <Button size="sm">Add Your First Vehicle</Button>
                </Link>
              </div>
            ) : (
              <div className="space-y-3">
                {vehicles.slice(0, 3).map((vehicle) => (
                  <div key={vehicle.id} className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium">{vehicle.title}</p>
                      <p className="text-xs text-muted-foreground">
                        ₹{vehicle.pricePerHour}/hour
                      </p>
                    </div>
                    <Badge className={getVehicleStatusColor(vehicle.available)}>
                      {vehicle.available ? 'Available' : 'Rented'}
                    </Badge>
                  </div>
                ))}
                {vehicles.length > 3 && (
                  <Link to="/renter/vehicles">
                    <Button variant="outline" size="sm" className="w-full">
                      View All ({vehicles.length})
                    </Button>
                  </Link>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default RenterDashboard;
