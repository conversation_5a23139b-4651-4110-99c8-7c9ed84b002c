import React, { useState } from 'react';
import { AlertTriangle, Upload, CheckCircle, X } from 'lucide-react';
import { Button } from '../ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Alert, AlertDescription } from '../ui/alert';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '../ui/dialog';
import { useAuth } from '../../context/AuthContext';
import { useToast } from '../../hooks/use-toast';
import { userAPI } from '../../services/api';

const AadhaarVerificationCard = () => {
  const [showModal, setShowModal] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [formData, setFormData] = useState({
    aadhaarNumber: '',
    name: '',
    file: null
  });
  const [errors, setErrors] = useState({});

  const { user, updateUser } = useAuth();
  const { toast } = useToast();

  const validateForm = () => {
    const newErrors = {};

    if (!formData.aadhaarNumber) {
      newErrors.aadhaarNumber = 'Aadhaar number is required';
    } else if (!/^\d{12}$/.test(formData.aadhaarNumber.replace(/\s/g, ''))) {
      newErrors.aadhaarNumber = 'Aadhaar number must be 12 digits';
    }

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!formData.file) {
      newErrors.file = 'Please upload Aadhaar document';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setUploading(true);

    try {
      // Mock API call for Aadhaar verification
      await userAPI.uploadAadhaar(formData.file);
      
      // Update user verification status
      updateUser({ aadhaarVerified: true });
      
      toast({
        title: "Aadhaar verified successfully",
        description: "Your identity has been verified. You can now book vehicles.",
      });
      
      setShowModal(false);
    } catch (error) {
      toast({
        title: "Verification failed",
        description: "Please try again or contact support.",
        variant: "destructive",
      });
    } finally {
      setUploading(false);
    }
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        setErrors(prev => ({ ...prev, file: 'File size must be less than 5MB' }));
        return;
      }
      
      if (!['image/jpeg', 'image/png', 'application/pdf'].includes(file.type)) {
        setErrors(prev => ({ ...prev, file: 'Only JPEG, PNG, and PDF files are allowed' }));
        return;
      }
      
      setFormData(prev => ({ ...prev, file }));
      setErrors(prev => ({ ...prev, file: '' }));
    }
  };

  const formatAadhaarNumber = (value) => {
    const cleaned = value.replace(/\D/g, '');
    const formatted = cleaned.replace(/(\d{4})(\d{4})(\d{4})/, '$1 $2 $3');
    return formatted;
  };

  if (user?.aadhaarVerified) {
    return null;
  }

  return (
    <>
      <Alert className="border-orange-200 bg-orange-50">
        <AlertTriangle className="h-4 w-4 text-orange-600" />
        <AlertDescription className="flex items-center justify-between">
          <div>
            <strong>Identity Verification Required</strong>
            <p className="text-sm mt-1">
              Please verify your Aadhaar to start booking vehicles. This is a one-time process for your security.
            </p>
          </div>
          <Dialog open={showModal} onOpenChange={setShowModal}>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                Verify Now
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Aadhaar Verification</DialogTitle>
                <DialogDescription>
                  Upload your Aadhaar card for identity verification. Your information is secure and encrypted.
                </DialogDescription>
              </DialogHeader>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="aadhaarNumber">Aadhaar Number</Label>
                  <Input
                    id="aadhaarNumber"
                    placeholder="1234 5678 9012"
                    value={formData.aadhaarNumber}
                    onChange={(e) => {
                      const formatted = formatAadhaarNumber(e.target.value);
                      if (formatted.replace(/\s/g, '').length <= 12) {
                        setFormData(prev => ({ ...prev, aadhaarNumber: formatted }));
                        setErrors(prev => ({ ...prev, aadhaarNumber: '' }));
                      }
                    }}
                    maxLength={14}
                  />
                  {errors.aadhaarNumber && (
                    <p className="text-sm text-destructive">{errors.aadhaarNumber}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="name">Name (as per Aadhaar)</Label>
                  <Input
                    id="name"
                    placeholder="Enter your full name"
                    value={formData.name}
                    onChange={(e) => {
                      setFormData(prev => ({ ...prev, name: e.target.value }));
                      setErrors(prev => ({ ...prev, name: '' }));
                    }}
                  />
                  {errors.name && (
                    <p className="text-sm text-destructive">{errors.name}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="file">Upload Aadhaar Document</Label>
                  <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center">
                    <Upload className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                    <div className="space-y-2">
                      <p className="text-sm text-muted-foreground">
                        Click to upload or drag and drop
                      </p>
                      <p className="text-xs text-muted-foreground">
                        JPEG, PNG or PDF (max 5MB)
                      </p>
                      <Input
                        id="file"
                        type="file"
                        accept=".jpg,.jpeg,.png,.pdf"
                        onChange={handleFileChange}
                        className="hidden"
                      />
                      <Label htmlFor="file" className="cursor-pointer">
                        <Button type="button" variant="outline" size="sm" asChild>
                          <span>Choose File</span>
                        </Button>
                      </Label>
                    </div>
                  </div>
                  
                  {formData.file && (
                    <div className="flex items-center gap-2 p-2 bg-muted rounded">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span className="text-sm flex-1">{formData.file.name}</span>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => setFormData(prev => ({ ...prev, file: null }))}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                  
                  {errors.file && (
                    <p className="text-sm text-destructive">{errors.file}</p>
                  )}
                </div>

                <div className="bg-muted p-3 rounded-lg">
                  <p className="text-xs text-muted-foreground">
                    <strong>Privacy Notice:</strong> Your Aadhaar information is encrypted and used only for verification purposes. We comply with all data protection regulations.
                  </p>
                </div>

                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    className="flex-1"
                    onClick={() => setShowModal(false)}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" className="flex-1" disabled={uploading}>
                    {uploading ? 'Verifying...' : 'Verify Aadhaar'}
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </AlertDescription>
      </Alert>
    </>
  );
};

export default AadhaarVerificationCard;
