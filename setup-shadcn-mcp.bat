@echo off
echo Setting up shadcn-ui MCP Server...
echo.

REM Check if GitHub token is provided
if "%1"=="" (
    echo Usage: setup-shadcn-mcp.bat ****************************************
    echo.
    echo Example: setup-shadcn-mcp.bat ghp_1234567890abcdef
    echo.
    echo Get your token from: https://github.com/settings/tokens
    echo No scopes needed - just generate a classic token
    pause
    exit /b 1
)

REM Set environment variable for current session
set GITHUB_PERSONAL_ACCESS_TOKEN=%1

REM Test the MCP server
echo Testing MCP server with your token...
npx @jpisnice/shadcn-ui-mcp-server --version

echo.
echo ✅ Setup complete! 
echo.
echo To use in this session:
echo   npx @jpisnice/shadcn-ui-mcp-server
echo.
echo To make permanent, add this to your system environment variables:
echo   GITHUB_PERSONAL_ACCESS_TOKEN=%1
echo.
pause
