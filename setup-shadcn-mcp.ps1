# PowerShell script to set up shadcn-ui MCP Server
param(
    [Parameter(Mandatory=$true)]
    [string]$GitHubToken
)

Write-Host "🚀 Setting up shadcn-ui MCP Server..." -ForegroundColor Green
Write-Host ""

# Validate token format
if (-not $GitHubToken.StartsWith("ghp_")) {
    Write-Host "❌ Invalid token format. GitHub tokens should start with 'ghp_'" -ForegroundColor Red
    Write-Host "Get your token from: https://github.com/settings/tokens" -ForegroundColor Yellow
    exit 1
}

# Set environment variable for current session
$env:GITHUB_PERSONAL_ACCESS_TOKEN = $GitHubToken

# Test the MCP server
Write-Host "🧪 Testing MCP server with your token..." -ForegroundColor Blue
try {
    npx @jpisnice/shadcn-ui-mcp-server --version
    Write-Host ""
    Write-Host "✅ MCP Server is working!" -ForegroundColor Green
} catch {
    Write-Host "❌ Error testing MCP server: $_" -ForegroundColor Red
    exit 1
}

# Update Claude Desktop config if it exists
$claudeConfigPath = "$env:USERPROFILE\.config\Claude\claude_desktop_config.json"
if (Test-Path $claudeConfigPath) {
    Write-Host "🔧 Updating Claude Desktop configuration..." -ForegroundColor Blue
    
    $config = Get-Content $claudeConfigPath | ConvertFrom-Json
    $config.mcpServers."shadcn-ui".env.GITHUB_PERSONAL_ACCESS_TOKEN = $GitHubToken
    $config | ConvertTo-Json -Depth 10 | Set-Content $claudeConfigPath
    
    Write-Host "✅ Claude Desktop configuration updated!" -ForegroundColor Green
}

Write-Host ""
Write-Host "🎉 Setup Complete!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Next Steps:" -ForegroundColor Yellow
Write-Host "1. Restart Claude Desktop (if using)" -ForegroundColor White
Write-Host "2. Test with: npx @jpisnice/shadcn-ui-mcp-server" -ForegroundColor White
Write-Host "3. In Claude, you can now ask about shadcn/ui components!" -ForegroundColor White
Write-Host ""
Write-Host "💡 Example prompts:" -ForegroundColor Cyan
Write-Host "   - 'Show me the button component from shadcn/ui'" -ForegroundColor Gray
Write-Host "   - 'List all available shadcn/ui components'" -ForegroundColor Gray
Write-Host "   - 'Get the dashboard-01 block implementation'" -ForegroundColor Gray
