# 🚗 Vehicle Rental Platform

A comprehensive vehicle rental platform built with React, Vite, and shadcn/ui components. This platform supports multiple user roles including users, renters, admins, and super admins.

## ✨ Features

### 🏠 **Landing Page**
- Modern, responsive design
- Feature highlights and vehicle types
- Call-to-action sections
- Mobile-friendly navigation

### 🔐 **Authentication System**
- Role-based login/signup (User, Renter, Admin, Super Admin)
- Demo credentials for testing
- Secure authentication flow
- Password visibility toggle

### 👤 **User Interface**
- **Dashboard**: Vehicle search and filtering, Aadhaar verification
- **Bookings**: View and manage rental bookings
- **Payments**: Payment history and receipt downloads
- **Vehicle Search**: Advanced filtering by type, location, price

### 🏪 **Renter Interface**
- **Dashboard**: Earnings overview and vehicle statistics
- **Vehicle Upload**: Add new vehicles with images and details
- **Vehicle Management**: Edit, delete, and toggle availability
- **History & Earnings**: Detailed rental history and revenue tracking

### 👨‍💼 **Admin Interface**
- **Dashboard**: Platform statistics and booking management
- **Live Activity**: Real-time platform monitoring
- **Booking Approval**: Approve/reject rental requests
- **User Management**: Monitor user activities

### 🔧 **Super Admin Interface**
- **Dashboard**: System health and global statistics
- **Admin Management**: Create, edit, and manage admin accounts
- **System Reports**: Generate and download various reports
- **Platform Analytics**: Comprehensive system insights

## 🛠️ **Technology Stack**

- **Frontend**: React 18 + Vite
- **UI Components**: shadcn/ui (Radix UI + Tailwind CSS)
- **Routing**: React Router DOM
- **Icons**: Lucide React
- **Styling**: Tailwind CSS
- **State Management**: React Context API
- **Notifications**: Custom toast system

## 📦 **shadcn/ui Components Used**

- Button, Input, Card, Badge, Alert
- Dialog, Sheet, Tabs, Table, Select
- Navigation Menu, Dropdown Menu, Avatar
- Progress, Separator, Skeleton, Toast
- Checkbox, Label, Textarea

## 🚀 **Getting Started**

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd Rental_App
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Open in browser**
   ```
   http://localhost:5173
   ```

## 🧪 **Demo Credentials**

### User Account
- **Email**: <EMAIL>
- **Password**: password
- **Role**: User

### Renter Account
- **Email**: <EMAIL>
- **Password**: password
- **Role**: Renter

### Admin Account
- **Email**: <EMAIL>
- **Password**: password
- **Role**: Admin

### Super Admin Account
- **Email**: <EMAIL>
- **Password**: password
- **Role**: Super Admin

## 📁 **Project Structure**

```
src/
├── components/
│   ├── ui/                 # shadcn/ui components
│   └── shared/             # Shared components
├── pages/
│   ├── user/              # User interface pages
│   ├── renter/            # Renter interface pages
│   ├── admin/             # Admin interface pages
│   └── super-admin/       # Super admin interface pages
├── context/               # React Context providers
├── services/              # API services and mock data
├── hooks/                 # Custom React hooks
├── lib/                   # Utility functions
└── routes.jsx             # Application routing
```

## 🎯 **Key Features Implemented**

### ✅ **Authentication & Authorization**
- Multi-role authentication system
- Protected routes based on user roles
- Session management with localStorage

### ✅ **Vehicle Management**
- Vehicle listing with images and details
- Search and filtering capabilities
- Availability management
- Pricing configuration

### ✅ **Booking System**
- Real-time booking requests
- Booking status management
- Payment tracking
- Booking history

### ✅ **Admin Controls**
- User and booking management
- Live activity monitoring
- System health tracking
- Report generation

### ✅ **Responsive Design**
- Mobile-first approach
- Adaptive layouts
- Touch-friendly interfaces
- Cross-browser compatibility

## 🧪 **Testing the Application**

1. **Start the development server**: `npm run dev`
2. **Navigate to the landing page**: http://localhost:5173
3. **Test authentication with demo credentials**
4. **Explore different user interfaces**
5. **Test vehicle booking flow**
6. **Check admin functionalities**

---

**Built with ❤️ using React, Vite, and shadcn/ui**
