import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from './context/AuthContext';

// Layout Components
import Layout from './components/shared/Layout';
import ProtectedRoute from './components/shared/ProtectedRoute';

// Auth Pages
import LoginPage from './pages/LoginPage';
import SignupPage from './pages/SignupPage';
import LandingPage from './pages/LandingPage';

// User Pages
import UserDashboard from './pages/user/Dashboard';
import UserBookings from './pages/user/Bookings';
import UserPayments from './pages/user/Payments';

// Renter Pages
import RenterDashboard from './pages/renter/Dashboard';
import RenterUpload from './pages/renter/Upload';
import RenterVehicles from './pages/renter/Vehicles';
import RenterHistory from './pages/renter/History';

// Admin Pages
import AdminDashboard from './pages/admin/Dashboard';
import AdminLive from './pages/admin/Live';

// Super Admin Pages
import SuperAdminDashboard from './pages/super-admin/Dashboard';
import SuperAdminAdmins from './pages/super-admin/Admins';

const AppRoutes = () => {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <Router>
      <Routes>
        {/* Public Routes */}
        <Route path="/" element={<LandingPage />} />
        <Route 
          path="/login" 
          element={user ? <Navigate to={`/${user.role}/dashboard`} /> : <LoginPage />} 
        />
        <Route 
          path="/signup" 
          element={user ? <Navigate to={`/${user.role}/dashboard`} /> : <SignupPage />} 
        />

        {/* Protected Routes */}
        <Route path="/user" element={
          <ProtectedRoute allowedRoles={['user']}>
            <Layout />
          </ProtectedRoute>
        }>
          <Route path="dashboard" element={<UserDashboard />} />
          <Route path="bookings" element={<UserBookings />} />
          <Route path="payments" element={<UserPayments />} />
        </Route>

        <Route path="/renter" element={
          <ProtectedRoute allowedRoles={['renter']}>
            <Layout />
          </ProtectedRoute>
        }>
          <Route path="dashboard" element={<RenterDashboard />} />
          <Route path="upload" element={<RenterUpload />} />
          <Route path="vehicles" element={<RenterVehicles />} />
          <Route path="history" element={<RenterHistory />} />
        </Route>

        <Route path="/admin" element={
          <ProtectedRoute allowedRoles={['admin', 'super-admin']}>
            <Layout />
          </ProtectedRoute>
        }>
          <Route path="dashboard" element={<AdminDashboard />} />
          <Route path="live" element={<AdminLive />} />
        </Route>

        <Route path="/super-admin" element={
          <ProtectedRoute allowedRoles={['super-admin']}>
            <Layout />
          </ProtectedRoute>
        }>
          <Route path="dashboard" element={<SuperAdminDashboard />} />
          <Route path="admins" element={<SuperAdminAdmins />} />
        </Route>

        {/* Redirect based on user role */}
        <Route path="/dashboard" element={
          user ? (
            <Navigate to={`/${user.role}/dashboard`} />
          ) : (
            <Navigate to="/login" />
          )
        } />

        {/* 404 Route */}
        <Route path="*" element={
          <div className="min-h-screen flex items-center justify-center">
            <div className="text-center">
              <h1 className="text-4xl font-bold text-gray-900 mb-4">404</h1>
              <p className="text-gray-600 mb-4">Page not found</p>
              <a href="/" className="text-primary hover:underline">Go back home</a>
            </div>
          </div>
        } />
      </Routes>
    </Router>
  );
};

export default AppRoutes;
