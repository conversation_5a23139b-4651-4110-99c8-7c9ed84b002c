import React, { useState, useEffect } from 'react';
import { Calendar, Clock, MapPin, Phone, Car, CreditCard, Download, X } from 'lucide-react';
import { <PERSON><PERSON> } from '../../components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card';
import { Badge } from '../../components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '../../components/ui/dialog';
import { Alert, AlertDescription } from '../../components/ui/alert';
import { Separator } from '../../components/ui/separator';
import { useAuth } from '../../context/AuthContext';
import { useToast } from '../../hooks/use-toast';
import { bookingAPI } from '../../services/api';

const Bookings = () => {
  const [bookings, setBookings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [cancellingId, setCancellingId] = useState(null);

  const { user } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    loadBookings();
  }, []);

  const loadBookings = async () => {
    try {
      const response = await bookingAPI.getByUserId(user.id);
      setBookings(response.data);
    } catch (error) {
      console.error('Error loading bookings:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCancelBooking = async (bookingId) => {
    setCancellingId(bookingId);
    try {
      await bookingAPI.cancel(bookingId);
      setBookings(prev => prev.map(booking => 
        booking.id === bookingId 
          ? { ...booking, status: 'cancelled' }
          : booking
      ));
      toast({
        title: "Booking Cancelled",
        description: "Your booking has been cancelled successfully.",
      });
    } catch (error) {
      toast({
        title: "Cancellation Failed",
        description: "Unable to cancel booking. Please try again.",
        variant: "destructive",
      });
    } finally {
      setCancellingId(null);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPaymentStatusColor = (status) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const activeBookings = bookings.filter(b => b.status === 'active' || b.status === 'pending');
  const pastBookings = bookings.filter(b => b.status === 'completed' || b.status === 'cancelled');

  const BookingCard = ({ booking }) => (
    <Card className="mb-4">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">{booking.vehicleTitle}</CardTitle>
          <div className="flex gap-2">
            <Badge className={getStatusColor(booking.status)}>
              {booking.status}
            </Badge>
            <Badge className={getPaymentStatusColor(booking.paymentStatus)}>
              {booking.paymentStatus}
            </Badge>
          </div>
        </div>
        <CardDescription>
          Booking ID: {booking.id}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span>{new Date(booking.startDate).toLocaleDateString()} - {new Date(booking.endDate).toLocaleDateString()}</span>
            </div>
            {booking.startTime && (
              <div className="flex items-center gap-2 text-sm">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span>{booking.startTime} - {booking.endTime}</span>
              </div>
            )}
            <div className="flex items-center gap-2 text-sm">
              <CreditCard className="h-4 w-4 text-muted-foreground" />
              <span className="font-semibold">₹{booking.totalAmount}</span>
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm">
              <MapPin className="h-4 w-4 text-muted-foreground" />
              <span>{booking.pickupLocation || 'Location not specified'}</span>
            </div>
            <div className="text-sm text-muted-foreground">
              Booked on: {new Date(booking.createdAt).toLocaleDateString()}
            </div>
          </div>
        </div>

        {booking.notes && (
          <div className="p-3 bg-muted rounded-lg">
            <p className="text-sm"><strong>Notes:</strong> {booking.notes}</p>
          </div>
        )}

        <div className="flex gap-2">
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                View Details
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Booking Details</DialogTitle>
                <DialogDescription>
                  Complete information about your booking
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-4">
                <div className="space-y-2">
                  <h4 className="font-semibold">Vehicle Information</h4>
                  <div className="text-sm space-y-1">
                    <div className="flex justify-between">
                      <span>Vehicle:</span>
                      <span>{booking.vehicleTitle}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Booking ID:</span>
                      <span>{booking.id}</span>
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="space-y-2">
                  <h4 className="font-semibold">Rental Period</h4>
                  <div className="text-sm space-y-1">
                    <div className="flex justify-between">
                      <span>Start Date:</span>
                      <span>{new Date(booking.startDate).toLocaleDateString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>End Date:</span>
                      <span>{new Date(booking.endDate).toLocaleDateString()}</span>
                    </div>
                    {booking.startTime && (
                      <>
                        <div className="flex justify-between">
                          <span>Start Time:</span>
                          <span>{booking.startTime}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>End Time:</span>
                          <span>{booking.endTime}</span>
                        </div>
                      </>
                    )}
                  </div>
                </div>

                <Separator />

                <div className="space-y-2">
                  <h4 className="font-semibold">Payment Information</h4>
                  <div className="text-sm space-y-1">
                    <div className="flex justify-between">
                      <span>Total Amount:</span>
                      <span className="font-semibold">₹{booking.totalAmount}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Payment Status:</span>
                      <Badge className={getPaymentStatusColor(booking.paymentStatus)} size="sm">
                        {booking.paymentStatus}
                      </Badge>
                    </div>
                  </div>
                </div>

                {booking.pickupLocation && (
                  <>
                    <Separator />
                    <div className="space-y-2">
                      <h4 className="font-semibold">Pickup Location</h4>
                      <p className="text-sm">{booking.pickupLocation}</p>
                    </div>
                  </>
                )}

                {booking.notes && (
                  <>
                    <Separator />
                    <div className="space-y-2">
                      <h4 className="font-semibold">Notes</h4>
                      <p className="text-sm">{booking.notes}</p>
                    </div>
                  </>
                )}
              </div>
            </DialogContent>
          </Dialog>

          {booking.paymentStatus === 'paid' && (
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Receipt
            </Button>
          )}

          {(booking.status === 'pending' || booking.status === 'active') && (
            <Button
              variant="destructive"
              size="sm"
              onClick={() => handleCancelBooking(booking.id)}
              disabled={cancellingId === booking.id}
            >
              {cancellingId === booking.id ? (
                'Cancelling...'
              ) : (
                <>
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </>
              )}
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="space-y-4">
          {[1, 2, 3].map(i => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-muted rounded w-1/2"></div>
                <div className="h-3 bg-muted rounded w-1/4"></div>
              </CardHeader>
              <CardContent>
                <div className="h-20 bg-muted rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">My Bookings</h1>
        <p className="text-muted-foreground">Manage your vehicle rentals</p>
      </div>

      {bookings.length === 0 ? (
        <Card>
          <CardContent className="text-center py-8">
            <Car className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-semibold mb-2">No bookings yet</h3>
            <p className="text-muted-foreground mb-4">
              You haven't made any vehicle bookings yet. Start exploring available vehicles!
            </p>
            <Button>Browse Vehicles</Button>
          </CardContent>
        </Card>
      ) : (
        <Tabs defaultValue="active" className="space-y-4">
          <TabsList>
            <TabsTrigger value="active">
              Active Bookings ({activeBookings.length})
            </TabsTrigger>
            <TabsTrigger value="past">
              Past Bookings ({pastBookings.length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="active" className="space-y-4">
            {activeBookings.length === 0 ? (
              <Card>
                <CardContent className="text-center py-8">
                  <p className="text-muted-foreground">No active bookings</p>
                </CardContent>
              </Card>
            ) : (
              activeBookings.map(booking => (
                <BookingCard key={booking.id} booking={booking} />
              ))
            )}
          </TabsContent>

          <TabsContent value="past" className="space-y-4">
            {pastBookings.length === 0 ? (
              <Card>
                <CardContent className="text-center py-8">
                  <p className="text-muted-foreground">No past bookings</p>
                </CardContent>
              </Card>
            ) : (
              pastBookings.map(booking => (
                <BookingCard key={booking.id} booking={booking} />
              ))
            )}
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
};

export default Bookings;
