import React, { createContext, useContext, useState, useEffect } from 'react';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // Mock user data for development
  const mockUsers = {
    '<EMAIL>': {
      id: '1',
      email: '<EMAIL>',
      name: '<PERSON>',
      role: 'user',
      aadhaarVerified: false,
      phone: '+91 **********'
    },
    '<EMAIL>': {
      id: '2',
      email: '<EMAIL>',
      name: '<PERSON>',
      role: 'renter',
      aadhaarVerified: true,
      documentsVerified: true,
      phone: '+91 **********'
    },
    '<EMAIL>': {
      id: '3',
      email: '<EMAIL>',
      name: 'Admin User',
      role: 'admin',
      phone: '+91 **********'
    },
    '<EMAIL>': {
      id: '4',
      email: '<EMAIL>',
      name: 'Super Admin',
      role: 'super-admin',
      phone: '+91 **********'
    }
  };

  useEffect(() => {
    // Check for stored user session
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      setUser(JSON.parse(storedUser));
    }
    setLoading(false);
  }, []);

  const login = async (email, password, role) => {
    setLoading(true);
    try {
      // Mock authentication
      const mockUser = mockUsers[email];
      if (mockUser && mockUser.role === role) {
        setUser(mockUser);
        localStorage.setItem('user', JSON.stringify(mockUser));
        return { success: true };
      } else {
        throw new Error('Invalid credentials or role mismatch');
      }
    } catch (error) {
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  const signup = async (userData) => {
    setLoading(true);
    try {
      // Mock signup
      const newUser = {
        id: Date.now().toString(),
        ...userData,
        aadhaarVerified: false,
        documentsVerified: false
      };
      setUser(newUser);
      localStorage.setItem('user', JSON.stringify(newUser));
      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('user');
  };

  const updateUser = (updates) => {
    const updatedUser = { ...user, ...updates };
    setUser(updatedUser);
    localStorage.setItem('user', JSON.stringify(updatedUser));
  };

  const value = {
    user,
    login,
    signup,
    logout,
    updateUser,
    loading,
    isAuthenticated: !!user
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
