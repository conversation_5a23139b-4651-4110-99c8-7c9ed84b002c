import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, Eye, ToggleLeft, ToggleRight, Car } from 'lucide-react';
import { <PERSON> } from 'react-router-dom';
import { But<PERSON> } from '../../components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card';
import { Badge } from '../../components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '../../components/ui/dialog';
import { Alert, AlertDescription } from '../../components/ui/alert';
import { useAuth } from '../../context/AuthContext';
import { useToast } from '../../hooks/use-toast';
import { vehicleAPI } from '../../services/api';

const RenterVehicles = () => {
  const [vehicles, setVehicles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [deletingId, setDeletingId] = useState(null);

  const { user } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    loadVehicles();
  }, []);

  const loadVehicles = async () => {
    try {
      const response = await vehicleAPI.getAll();
      // Filter vehicles owned by current renter
      const myVehicles = response.data.filter(v => v.renterId === user.id);
      setVehicles(myVehicles);
    } catch (error) {
      console.error('Error loading vehicles:', error);
    } finally {
      setLoading(false);
    }
  };

  const toggleAvailability = async (vehicleId, currentStatus) => {
    try {
      await vehicleAPI.update(vehicleId, { available: !currentStatus });
      setVehicles(prev => prev.map(vehicle => 
        vehicle.id === vehicleId 
          ? { ...vehicle, available: !currentStatus }
          : vehicle
      ));
      toast({
        title: "Status Updated",
        description: `Vehicle is now ${!currentStatus ? 'available' : 'unavailable'} for rent.`,
      });
    } catch (error) {
      toast({
        title: "Update Failed",
        description: "Unable to update vehicle status. Please try again.",
        variant: "destructive",
      });
    }
  };

  const deleteVehicle = async (vehicleId) => {
    setDeletingId(vehicleId);
    try {
      await vehicleAPI.delete(vehicleId);
      setVehicles(prev => prev.filter(vehicle => vehicle.id !== vehicleId));
      toast({
        title: "Vehicle Deleted",
        description: "Vehicle has been removed from your listings.",
      });
    } catch (error) {
      toast({
        title: "Delete Failed",
        description: "Unable to delete vehicle. Please try again.",
        variant: "destructive",
      });
    } finally {
      setDeletingId(null);
    }
  };

  const getStatusColor = (available) => {
    return available ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'car':
        return '🚗';
      case 'bike':
        return '🏍️';
      case 'cycle':
        return '🚲';
      default:
        return '🚗';
    }
  };

  const availableVehicles = vehicles.filter(v => v.available);
  const unavailableVehicles = vehicles.filter(v => !v.available);

  const VehicleCard = ({ vehicle }) => (
    <Card className="overflow-hidden">
      <div className="relative">
        <img
          src={vehicle.images?.[0] || '/api/placeholder/300/200'}
          alt={vehicle.title}
          className="w-full h-48 object-cover"
        />
        <div className="absolute top-2 left-2">
          <Badge variant="secondary">
            {getTypeIcon(vehicle.type)} {vehicle.type}
          </Badge>
        </div>
        <div className="absolute top-2 right-2">
          <Badge className={getStatusColor(vehicle.available)}>
            {vehicle.available ? 'Available' : 'Unavailable'}
          </Badge>
        </div>
      </div>

      <CardHeader>
        <CardTitle className="text-lg">{vehicle.title}</CardTitle>
        <CardDescription className="line-clamp-2">
          {vehicle.description}
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <div className="font-semibold">₹{vehicle.pricePerHour}/hour</div>
            <div className="text-sm text-muted-foreground">₹{vehicle.pricePerDay}/day</div>
          </div>
          <div className="text-right">
            <div className="text-sm text-muted-foreground">Location</div>
            <div className="text-sm">{vehicle.location}</div>
          </div>
        </div>

        {vehicle.features && vehicle.features.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {vehicle.features.slice(0, 3).map((feature, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {feature}
              </Badge>
            ))}
            {vehicle.features.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{vehicle.features.length - 3} more
              </Badge>
            )}
          </div>
        )}

        <div className="flex gap-2">
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm" className="flex-1">
                <Eye className="h-4 w-4 mr-2" />
                View
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>{vehicle.title}</DialogTitle>
                <DialogDescription>Vehicle details and information</DialogDescription>
              </DialogHeader>
              
              <div className="space-y-4">
                <img
                  src={vehicle.images?.[0] || '/api/placeholder/400/250'}
                  alt={vehicle.title}
                  className="w-full h-64 object-cover rounded-lg"
                />
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-semibold mb-2">Vehicle Details</h4>
                    <div className="space-y-1 text-sm">
                      <div>Type: <span className="capitalize">{vehicle.type}</span></div>
                      <div>Hourly Rate: ₹{vehicle.pricePerHour}</div>
                      <div>Daily Rate: ₹{vehicle.pricePerDay}</div>
                      <div>Location: {vehicle.location}</div>
                      <div>Status: <Badge className={getStatusColor(vehicle.available)} size="sm">
                        {vehicle.available ? 'Available' : 'Unavailable'}
                      </Badge></div>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold mb-2">Features</h4>
                    <div className="flex flex-wrap gap-1">
                      {vehicle.features?.map((feature, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {feature}
                        </Badge>
                      )) || <span className="text-sm text-muted-foreground">No features listed</span>}
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">Description</h4>
                  <p className="text-sm text-muted-foreground">{vehicle.description}</p>
                </div>
              </div>
            </DialogContent>
          </Dialog>

          <Button
            variant="outline"
            size="sm"
            onClick={() => toggleAvailability(vehicle.id, vehicle.available)}
          >
            {vehicle.available ? (
              <>
                <ToggleRight className="h-4 w-4 mr-2" />
                Disable
              </>
            ) : (
              <>
                <ToggleLeft className="h-4 w-4 mr-2" />
                Enable
              </>
            )}
          </Button>

          <Button variant="outline" size="sm">
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>

          <Dialog>
            <DialogTrigger asChild>
              <Button variant="destructive" size="sm">
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Delete Vehicle</DialogTitle>
                <DialogDescription>
                  Are you sure you want to delete "{vehicle.title}"? This action cannot be undone.
                </DialogDescription>
              </DialogHeader>
              <div className="flex gap-2 justify-end">
                <DialogTrigger asChild>
                  <Button variant="outline">Cancel</Button>
                </DialogTrigger>
                <Button
                  variant="destructive"
                  onClick={() => deleteVehicle(vehicle.id)}
                  disabled={deletingId === vehicle.id}
                >
                  {deletingId === vehicle.id ? 'Deleting...' : 'Delete'}
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3].map(i => (
            <Card key={i} className="animate-pulse">
              <div className="h-48 bg-muted"></div>
              <CardHeader>
                <div className="h-4 bg-muted rounded w-3/4"></div>
                <div className="h-3 bg-muted rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="h-20 bg-muted rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">My Vehicles</h1>
          <p className="text-muted-foreground">Manage your vehicle listings</p>
        </div>
        <Link to="/renter/upload">
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Add Vehicle
          </Button>
        </Link>
      </div>

      {vehicles.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <Car className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-semibold mb-2">No vehicles listed</h3>
            <p className="text-muted-foreground mb-6">
              Start earning by listing your first vehicle for rent
            </p>
            <Link to="/renter/upload">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Your First Vehicle
              </Button>
            </Link>
          </CardContent>
        </Card>
      ) : (
        <Tabs defaultValue="all" className="space-y-4">
          <TabsList>
            <TabsTrigger value="all">
              All Vehicles ({vehicles.length})
            </TabsTrigger>
            <TabsTrigger value="available">
              Available ({availableVehicles.length})
            </TabsTrigger>
            <TabsTrigger value="unavailable">
              Unavailable ({unavailableVehicles.length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {vehicles.map(vehicle => (
                <VehicleCard key={vehicle.id} vehicle={vehicle} />
              ))}
            </div>
          </TabsContent>

          <TabsContent value="available" className="space-y-4">
            {availableVehicles.length === 0 ? (
              <Card>
                <CardContent className="text-center py-8">
                  <p className="text-muted-foreground">No available vehicles</p>
                </CardContent>
              </Card>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {availableVehicles.map(vehicle => (
                  <VehicleCard key={vehicle.id} vehicle={vehicle} />
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="unavailable" className="space-y-4">
            {unavailableVehicles.length === 0 ? (
              <Card>
                <CardContent className="text-center py-8">
                  <p className="text-muted-foreground">No unavailable vehicles</p>
                </CardContent>
              </Card>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {unavailableVehicles.map(vehicle => (
                  <VehicleCard key={vehicle.id} vehicle={vehicle} />
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
};

export default RenterVehicles;
