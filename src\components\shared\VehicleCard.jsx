import React, { useState } from 'react';
import { MapPin, Clock, Star, Phone, Car, Bike, CircleDot } from 'lucide-react';
import { Button } from '../ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '../ui/dialog';
import BookingModal from './BookingModal';

const VehicleCard = ({ vehicle }) => {
  const [showBookingModal, setShowBookingModal] = useState(false);

  const getVehicleIcon = (type) => {
    switch (type) {
      case 'car':
        return <Car className="h-4 w-4" />;
      case 'bike':
        return <Bike className="h-4 w-4" />;
      case 'cycle':
        return <CircleDot className="h-4 w-4" />;
      default:
        return <Car className="h-4 w-4" />;
    }
  };

  const getTypeColor = (type) => {
    switch (type) {
      case 'car':
        return 'bg-blue-100 text-blue-800';
      case 'bike':
        return 'bg-green-100 text-green-800';
      case 'cycle':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <>
      <Card className="overflow-hidden hover:shadow-lg transition-shadow">
        <div className="relative">
          <img
            src={vehicle.images?.[0] || '/api/placeholder/300/200'}
            alt={vehicle.title}
            className="w-full h-48 object-cover"
          />
          <div className="absolute top-2 left-2">
            <Badge className={getTypeColor(vehicle.type)}>
              {getVehicleIcon(vehicle.type)}
              <span className="ml-1 capitalize">{vehicle.type}</span>
            </Badge>
          </div>
          <div className="absolute top-2 right-2">
            {vehicle.available ? (
              <Badge className="bg-green-100 text-green-800">Available</Badge>
            ) : (
              <Badge variant="destructive">Not Available</Badge>
            )}
          </div>
        </div>

        <CardHeader>
          <CardTitle className="text-lg">{vehicle.title}</CardTitle>
          <CardDescription className="line-clamp-2">
            {vehicle.description}
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <MapPin className="h-4 w-4" />
            {vehicle.location}
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span className="font-semibold">₹{vehicle.pricePerHour}/hour</span>
              </div>
              <div className="text-sm text-muted-foreground">
                ₹{vehicle.pricePerDay}/day
              </div>
            </div>

            <div className="flex items-center gap-1">
              <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
              <span className="text-sm font-medium">4.5</span>
              <span className="text-sm text-muted-foreground">(12)</span>
            </div>
          </div>

          {vehicle.features && vehicle.features.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {vehicle.features.slice(0, 3).map((feature, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {feature}
                </Badge>
              ))}
              {vehicle.features.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{vehicle.features.length - 3} more
                </Badge>
              )}
            </div>
          )}

          <div className="flex gap-2">
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline" className="flex-1">
                  View Details
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>{vehicle.title}</DialogTitle>
                  <DialogDescription>
                    Complete vehicle information and booking details
                  </DialogDescription>
                </DialogHeader>
                
                <div className="space-y-4">
                  <img
                    src={vehicle.images?.[0] || '/api/placeholder/400/250'}
                    alt={vehicle.title}
                    className="w-full h-64 object-cover rounded-lg"
                  />
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-semibold mb-2">Vehicle Details</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>Type:</span>
                          <span className="capitalize">{vehicle.type}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Location:</span>
                          <span>{vehicle.location}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Hourly Rate:</span>
                          <span>₹{vehicle.pricePerHour}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Daily Rate:</span>
                          <span>₹{vehicle.pricePerDay}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="font-semibold mb-2">Owner Details</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>Name:</span>
                          <span>{vehicle.renterName}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Phone className="h-3 w-3" />
                          <span>{vehicle.renterPhone}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                          <span>4.5 (12 reviews)</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-semibold mb-2">Description</h4>
                    <p className="text-sm text-muted-foreground">{vehicle.description}</p>
                  </div>

                  {vehicle.features && vehicle.features.length > 0 && (
                    <div>
                      <h4 className="font-semibold mb-2">Features</h4>
                      <div className="flex flex-wrap gap-2">
                        {vehicle.features.map((feature, index) => (
                          <Badge key={index} variant="outline">
                            {feature}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </DialogContent>
            </Dialog>

            <Button 
              className="flex-1" 
              disabled={!vehicle.available}
              onClick={() => setShowBookingModal(true)}
            >
              {vehicle.available ? 'Book Now' : 'Not Available'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {showBookingModal && (
        <BookingModal
          vehicle={vehicle}
          open={showBookingModal}
          onClose={() => setShowBookingModal(false)}
        />
      )}
    </>
  );
};

export default VehicleCard;
