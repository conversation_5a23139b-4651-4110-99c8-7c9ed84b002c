# 📝 Product Requirements Document (Frontend Only)
**Vehicle Rental Platform**

---

## 1. Product Title:
**Vehicle Rental Platform – Frontend**

---

## 2. Objective:
To design and develop a responsive, role-based web frontend that allows users to rent vehicles (bike, car, cycle) from nearby renters, and provides admin and super admin panels for system management and monitoring.

---

## 3. Target Roles and Users:
- **User** (Customer looking to rent a vehicle)
- **Renter** (Person uploading their vehicle for rent)
- **Admin** (Oversees system operations and payments)
- **Super Admin** (Platform owner/founder with complete access)

---

## 4. Key Features by Role:

### 4.1 User Interface
- Sign Up / Log In
- Upload Aadhaar card (one-time verification)
- Search and browse vehicles (with filters, location-based in future)
- View vehicle details and book rentals
- View previous bookings
- Download/view payment receipts
- Responsive UI (mobile and desktop)

### 4.2 Renter Interface
- Sign Up / Log In
- Upload Aadhaar and vehicle documents
- Add new vehicles (title, description, images, location)
- View all listed vehicles
- View rent history
- View earnings summary

### 4.3 Admin Interface
- Admin login
- View all rental orders
- Monitor all payment transactions
- View live activity (e.g., who rented what and when)
- Search/filter bookings by user, renter, date

### 4.4 Super Admin Interface
- Super admin login
- All admin features +
  - View total earnings and profit
  - Add/Remove admin accounts
  - Access complete system reports

---

## 5. UI Pages (Screens):

| Page | Description |
|------|-------------|
| `/` | Landing page |
| `/login` | Common login for all roles |
| `/signup` | Signup form with role selection |
| `/user/dashboard` | User home with vehicle search, Aadhaar upload if needed |
| `/user/bookings` | User’s previous bookings |
| `/user/payments` | User’s payment history |
| `/renter/dashboard` | Renter’s home panel |
| `/renter/upload` | Upload vehicle form |
| `/renter/vehicles` | List of uploaded vehicles |
| `/renter/history` | Rent history and earnings |
| `/admin/dashboard` | View orders and payments |
| `/admin/live` | Live booking activity |
| `/super-admin/dashboard` | Global stats and profit reports |
| `/super-admin/admins` | Admin management UI |

---

## 6. Component Requirements:

- `VehicleCard`: Shows vehicle info + book button
- `AadhaarForm`: Upload Aadhaar details
- `BookingModal`: Select date/time, confirm booking
- `Sidebar`: Role-based navigation
- `Header/Nav`: Common for all users
- `ProtectedRoute`: Role-based routing guard
- `Toast/Error UI`: For user feedback

---

## 7. State Management:
- Context API for:
  - Authentication (AuthContext)
  - Role-based access (RoleContext)
- Axios for API calls

---

## 8. Folder Structure (Proposal):
```
/src
  /components
  /pages
    /user
    /renter
    /admin
    /super-admin
  /context
  /services
  App.js
  routes.js
```

---

## 9. Dependencies (Libraries):
- `react-router-dom` – routing
- `axios` – API requests
- `tailwindcss` or `bootstrap` – UI styling
- `uuid`, `moment.js` – utility (if needed)
- `react-toastify` – notifications

---

## 10. Out of Scope (Frontend):
- Backend APIs
- Aadhaar OTP verification logic (mocked for now)
- Real-time updates (can be simulated)

---

## 11. Milestones:

| Phase | Description | Est. Time |
|-------|-------------|-----------|
| Phase 1 | Setup project, routing, auth context | 1 day |
| Phase 2 | User interface and booking flow | 2–3 days |
| Phase 3 | Renter dashboard and vehicle upload | 2–3 days |
| Phase 4 | Admin and Super Admin dashboards | 2–3 days |
| Phase 5 | Role-based route protection, UI polish | 1–2 days |