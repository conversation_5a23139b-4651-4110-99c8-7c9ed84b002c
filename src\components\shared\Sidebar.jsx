import React from 'react';
import { NavLink } from 'react-router-dom';
import { X, Home, Car, CreditCard, Upload, History, Users, Activity, Settings, BarChart3 } from 'lucide-react';
import { Button } from '../ui/button';
import { Sheet, SheetContent, SheetHeader, SheetTitle } from '../ui/sheet';
import { useAuth } from '../../context/AuthContext';
import { cn } from '../../lib/utils';

const Sidebar = ({ open, onClose }) => {
  const { user } = useAuth();

  const getNavigationItems = () => {
    switch (user?.role) {
      case 'user':
        return [
          { to: '/user/dashboard', icon: Home, label: 'Dashboard' },
          { to: '/user/bookings', icon: Car, label: 'My Bookings' },
          { to: '/user/payments', icon: CreditCard, label: 'Payments' },
        ];
      case 'renter':
        return [
          { to: '/renter/dashboard', icon: Home, label: 'Dashboard' },
          { to: '/renter/upload', icon: Upload, label: 'Add Vehicle' },
          { to: '/renter/vehicles', icon: Car, label: 'My Vehicles' },
          { to: '/renter/history', icon: History, label: 'Rental History' },
        ];
      case 'admin':
        return [
          { to: '/admin/dashboard', icon: Home, label: 'Dashboard' },
          { to: '/admin/live', icon: Activity, label: 'Live Activity' },
        ];
      case 'super-admin':
        return [
          { to: '/super-admin/dashboard', icon: BarChart3, label: 'Dashboard' },
          { to: '/admin/dashboard', icon: Home, label: 'Admin Panel' },
          { to: '/admin/live', icon: Activity, label: 'Live Activity' },
          { to: '/super-admin/admins', icon: Users, label: 'Manage Admins' },
        ];
      default:
        return [];
    }
  };

  const navigationItems = getNavigationItems();

  const SidebarContent = () => (
    <div className="flex flex-col h-full">
      <div className="p-6">
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">VR</span>
          </div>
          <span className="font-semibold text-lg">Vehicle Rental</span>
        </div>
      </div>

      <nav className="flex-1 px-4 pb-4">
        <ul className="space-y-2">
          {navigationItems.map((item) => (
            <li key={item.to}>
              <NavLink
                to={item.to}
                onClick={onClose}
                className={({ isActive }) =>
                  cn(
                    "flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors",
                    isActive
                      ? "bg-primary text-primary-foreground"
                      : "text-muted-foreground hover:text-foreground hover:bg-muted"
                  )
                }
              >
                <item.icon className="h-4 w-4" />
                {item.label}
              </NavLink>
            </li>
          ))}
        </ul>
      </nav>

      <div className="p-4 border-t">
        <div className="text-xs text-muted-foreground">
          Logged in as: <span className="font-medium">{user?.role}</span>
        </div>
      </div>
    </div>
  );

  return (
    <>
      {/* Desktop Sidebar */}
      <div className="hidden lg:block fixed inset-y-0 left-0 w-64 bg-white border-r border-border">
        <SidebarContent />
      </div>

      {/* Mobile Sidebar */}
      <Sheet open={open} onOpenChange={onClose}>
        <SheetContent side="left" className="w-64 p-0">
          <SidebarContent />
        </SheetContent>
      </Sheet>
    </>
  );
};

export default Sidebar;
