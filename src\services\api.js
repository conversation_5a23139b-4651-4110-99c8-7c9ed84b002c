import axios from 'axios';

// Mock API base URL - replace with actual backend URL
const API_BASE_URL = 'http://localhost:3001/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    if (user.token) {
      config.headers.Authorization = `Bearer ${user.token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Mock data for development
const mockVehicles = [
  {
    id: '1',
    title: 'Honda Activa 6G',
    description: 'Well-maintained scooter, perfect for city rides',
    type: 'bike',
    pricePerHour: 50,
    pricePerDay: 400,
    location: 'Koramangala, Bangalore',
    images: ['/api/placeholder/300/200'],
    available: true,
    renterId: '2',
    renterName: 'Jane Smith',
    renterPhone: '+91 9876543211',
    features: ['Helmet included', 'Full tank', 'Recently serviced']
  },
  {
    id: '2',
    title: 'Maruti Swift Dzire',
    description: 'Comfortable sedan for long trips',
    type: 'car',
    pricePerHour: 150,
    pricePerDay: 1200,
    location: 'Indiranagar, Bangalore',
    images: ['/api/placeholder/300/200'],
    available: true,
    renterId: '2',
    renterName: 'Jane Smith',
    renterPhone: '+91 9876543211',
    features: ['AC', 'Music system', 'GPS']
  },
  {
    id: '3',
    title: 'Hero Cycle',
    description: 'Eco-friendly bicycle for short distances',
    type: 'cycle',
    pricePerHour: 20,
    pricePerDay: 100,
    location: 'MG Road, Bangalore',
    images: ['/api/placeholder/300/200'],
    available: true,
    renterId: '2',
    renterName: 'Jane Smith',
    renterPhone: '+91 9876543211',
    features: ['Lightweight', 'Good condition', 'Lock included']
  }
];

const mockBookings = [
  {
    id: '1',
    vehicleId: '1',
    vehicleTitle: 'Honda Activa 6G',
    userId: '1',
    userName: 'John Doe',
    startDate: '2024-01-15',
    endDate: '2024-01-16',
    totalAmount: 400,
    status: 'completed',
    paymentStatus: 'paid',
    createdAt: '2024-01-14T10:00:00Z'
  },
  {
    id: '2',
    vehicleId: '2',
    vehicleTitle: 'Maruti Swift Dzire',
    userId: '1',
    userName: 'John Doe',
    startDate: '2024-01-20',
    endDate: '2024-01-22',
    totalAmount: 2400,
    status: 'active',
    paymentStatus: 'paid',
    createdAt: '2024-01-19T15:30:00Z'
  }
];

// API functions
export const vehicleAPI = {
  getAll: () => Promise.resolve({ data: mockVehicles }),
  getById: (id) => Promise.resolve({ data: mockVehicles.find(v => v.id === id) }),
  create: (vehicleData) => Promise.resolve({ data: { id: Date.now().toString(), ...vehicleData } }),
  update: (id, vehicleData) => Promise.resolve({ data: { id, ...vehicleData } }),
  delete: (id) => Promise.resolve({ data: { success: true } }),
  search: (filters) => Promise.resolve({ data: mockVehicles.filter(v => 
    (!filters.type || v.type === filters.type) &&
    (!filters.location || v.location.toLowerCase().includes(filters.location.toLowerCase()))
  )})
};

export const bookingAPI = {
  getAll: () => Promise.resolve({ data: mockBookings }),
  getByUserId: (userId) => Promise.resolve({ data: mockBookings.filter(b => b.userId === userId) }),
  getByRenterId: (renterId) => Promise.resolve({ data: mockBookings.filter(b => 
    mockVehicles.find(v => v.id === b.vehicleId)?.renterId === renterId
  )}),
  create: (bookingData) => Promise.resolve({ data: { id: Date.now().toString(), ...bookingData } }),
  update: (id, bookingData) => Promise.resolve({ data: { id, ...bookingData } }),
  cancel: (id) => Promise.resolve({ data: { id, status: 'cancelled' } })
};

export const userAPI = {
  uploadAadhaar: (file) => Promise.resolve({ data: { verified: true } }),
  uploadDocuments: (files) => Promise.resolve({ data: { verified: true } }),
  getProfile: (id) => Promise.resolve({ data: { id, verified: true } })
};

export const adminAPI = {
  getStats: () => Promise.resolve({ 
    data: { 
      totalBookings: 150, 
      totalRevenue: 45000, 
      activeUsers: 89, 
      totalVehicles: 45 
    } 
  }),
  getAllBookings: () => Promise.resolve({ data: mockBookings }),
  getAllUsers: () => Promise.resolve({ data: [] })
};

export default api;
