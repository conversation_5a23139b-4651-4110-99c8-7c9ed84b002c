import { Button } from './ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from './ui/card'
import { Input } from './ui/input'
import { Badge } from './ui/badge'

export function ShadcnExample() {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>shadcn/ui Components</CardTitle>
          <CardDescription>
            Here are some examples of shadcn/ui components in action
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Badge>Default</Badge>
            <Badge variant="secondary">Secondary</Badge>
            <Badge variant="destructive">Destructive</Badge>
            <Badge variant="outline">Outline</Badge>
          </div>
          
          <div className="space-y-2">
            <Input placeholder="Enter your name..." />
            <Input placeholder="Enter your email..." type="email" />
          </div>
          
          <div className="flex gap-2">
            <Button>Default Button</Button>
            <Button variant="secondary">Secondary</Button>
            <Button variant="outline">Outline</Button>
            <Button variant="ghost">Ghost</Button>
          </div>
        </CardContent>
        <CardFooter>
          <p className="text-sm text-muted-foreground">
            All components are fully customizable and accessible
          </p>
        </CardFooter>
      </Card>
    </div>
  )
}
